<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { goto } from '$app/navigation';
    import { checkSubscriptionStatus } from '$lib/services/subscriptionService';
    import { authStore } from '$lib/stores/auth';
    import type { User } from '@supabase/supabase-js';

    export let isOpen = false;

    let user: User | null = null;
    let checkingSubscription = false;
    let visibilityHandler: () => void;

    // Subscribe to auth store to get current user
    const unsubscribeAuth = authStore.subscribe((authState) => {
        user = authState.user;
    });

    async function handleFinishPayment() {
        if (!user || checkingSubscription) return;
        checkingSubscription = true;

        try {
            const hasActiveSubscription = await checkSubscriptionStatus(user.id);

            if (hasActiveSubscription) {
                goto('/app');
            } else {
                isOpen = false;
                alert(
                    'No active subscription found. Please try again or contact support if you completed payment.'
                );
            }
        } catch (error) {
            console.error('Error checking subscription:', error);
            alert('Unable to check subscription status. Please try again or contact support.');
        } finally {
            checkingSubscription = false;
        }
    }

    function handleChooseDifferent() {
        isOpen = false;
    }

    function handleCancel() {
        goto('/');
    }

    function handleBackdropClick(event: MouseEvent) {
        if (event.target === event.currentTarget) {
            handleChooseDifferent();
        }
    }

    onMount(() => {
        // When tab or app regains focus/visibility, retry the payment check
        visibilityHandler = () => {
            if (isOpen && document.visibilityState === 'visible') {
                handleFinishPayment();
            }
        };

        document.addEventListener('visibilitychange', visibilityHandler);
        window.addEventListener('focus', visibilityHandler);
    });

    onDestroy(() => {
        document.removeEventListener('visibilitychange', visibilityHandler);
        window.removeEventListener('focus', visibilityHandler);
        unsubscribeAuth();
    });
</script>

{#if isOpen}
    <!-- Modal backdrop -->
    <div
        class="fixed inset-0 z-50 flex items-center justify-center py-4"
        on:click={handleBackdropClick}
        on:keydown={(e) => e.key === 'Escape' && handleChooseDifferent()}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        tabindex="-1"
    >
        <!-- Modal content -->
        <div class="bg-white h-screen rounded-lg shadow-xl w-full mx-2 p-6">
            <!-- Header -->
            <div class="text-center mb-6">
                <h2 id="modal-title" class="text-xl font-semibold text-gray-900 mb-2">
                    Processing Your Subscription
                </h2>
                <p class="text-gray-600 text-sm">
                    Complete your payment and return here
                </p>
            </div>

            <!-- Processing animation -->
            <div class="flex justify-center mb-8">
                <div class="relative">
                    <!-- Outer spinning ring -->
                    <div class="animate-spin rounded-full h-16 w-16 border-4 border-blue-200"></div>
                    <!-- Inner spinning ring -->
                    <div
                        class="animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"
                        style="animation-duration: 1.5s;"
                    ></div>
                    <!-- Center icon -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Status message -->
            <div class="text-center mb-8">
                <p class="text-sm text-gray-500">
                    Complete your payment in browser window and return to this page.
                </p>
            </div>

            <!-- Action buttons -->
            <div class="space-y-3">
                <!-- Primary action - Finish Payment -->
                <button
                    on:click={handleFinishPayment}
                    disabled={checkingSubscription}
                    class="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                    {#if checkingSubscription}
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Checking...
                    {:else}
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        I Finished Payment
                    {/if}
                </button>

                <!-- Secondary action - Choose Different -->
                <button
                    on:click={handleChooseDifferent}
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                    Choose Different Subscription
                </button>

                <!-- Cancel -->
                <button
                    on:click={handleCancel}
                    class="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Exit
                </button>
            </div>

            <!-- Help text -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <p class="text-xs text-gray-500 text-center">
                    Having trouble? The payment window should open automatically.
                    If it doesn't, please try again or contact support.
                </p>
            </div>
        </div>
    </div>
{/if}

<style>
    /* Prevent page scroll when modal is open */
    :global(body.modal-open) {
        overflow: hidden;
    }
</style>
